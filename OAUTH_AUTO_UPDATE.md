# OAuth凭据自动更新工具

## 🎯 功能概述

这个自动更新工具解决了Gemini CLI OpenAI项目中的OAuth凭据管理问题，特别是ByteString转换错误。

### ✨ 主要功能

- ✅ **自动检测凭据路径** - 智能检测Windows Gemini CLI凭据位置
- ✅ **Unicode字符清理** - 自动清理导致ByteString错误的Unicode字符
- ✅ **环境变量更新** - 自动更新`.dev.vars`文件
- ✅ **无用脚本清理** - 清理过时的脚本文件
- ✅ **自动备份** - 所有操作都有备份，可安全恢复
- ✅ **凭据验证** - 验证OAuth token格式和有效期

## 🚀 使用方法

### 基本用法

```bash
# 自动检测并更新OAuth凭据
npm run update-oauth

# 或直接运行脚本
node update_oauth_credentials.js
```

### 高级用法

```bash
# 显示帮助信息
node update_oauth_credentials.js --help

# 仅清理无用脚本文件
npm run update-oauth:clean
node update_oauth_credentials.js --clean

# 使用指定的凭据文件
node update_oauth_credentials.js /path/to/custom_creds.json
```

## 🔍 自动检测路径

工具会按以下顺序自动检测OAuth凭据文件：

1. `C:\Users\<USER>\.gemini\oauth_creds.json` (Windows标准路径)
2. `I:/gemini/oauth_creds.json` (旧版路径)
3. `./oauth_creds.json` (当前目录)
4. `../oauth_creds.json` (上级目录)

## 🧹 清理的无用脚本

以下过时的脚本文件会被自动清理：

- `../clean_oauth_credentials.js`
- `./scripts/check-oauth.js`
- `./scripts/start-with-oauth-check.js`

所有删除的文件都会备份到`oauth_backups`目录。

## 📋 更新流程

1. **检测凭据** - 自动找到OAuth凭据文件
2. **验证格式** - 检查凭据完整性和有效期
3. **清理字符** - 移除导致ByteString错误的Unicode字符
4. **备份配置** - 备份当前`.dev.vars`文件
5. **清理脚本** - 删除无用的脚本文件
6. **更新环境** - 更新`.dev.vars`文件
7. **测试连接** - 验证新凭据是否工作

## 🛠️ 故障排除

### ByteString错误已解决

如果之前遇到以下错误：
```
Cannot convert argument to a ByteString because the character at index 30 has a value of 9888 which is greater than 255
```

运行自动更新工具即可解决：
```bash
npm run update-oauth
```

### Token过期处理

如果token已过期，工具会显示警告：
```
⚠️ 警告: Token已过期 (过期时间: 2025-01-30 08:37:10)
建议重新运行 gemini 命令获取新token
```

解决方法：
1. 运行 `gemini` 命令
2. 选择 "Login with Google"
3. 完成认证后重新运行更新工具

### 恢复备份

如果更新后出现问题，可以从备份恢复：

```bash
# 查看备份文件
ls oauth_backups/

# 恢复.dev.vars文件
cp oauth_backups/.dev.vars.backup.{timestamp} .dev.vars

# 恢复删除的脚本（如需要）
cp oauth_backups/clean_oauth_credentials.js.backup ../clean_oauth_credentials.js
```

## 📝 后续步骤

更新完成后：

1. **重启服务器**
   ```bash
   npm run dev
   ```

2. **测试API功能**
   ```bash
   node test-api.js
   ```

3. **验证服务正常**
   - 访问 http://127.0.0.1:8787
   - 测试API端点

## 🔧 配置说明

更新后的`.dev.vars`文件包含：

- `GCP_SERVICE_ACCOUNT` - 清理后的OAuth凭据
- `OPENAI_API_KEY` - API认证密钥
- `ENABLE_FAKE_THINKING` - 启用假思考模式
- `ENABLE_REAL_THINKING` - 启用真实思考模式
- `STREAM_THINKING_AS_CONTENT` - 流式思考内容
- `ENABLE_AUTO_MODEL_SWITCHING` - 自动模型切换

## 🎉 完成

运行自动更新工具后，你的Gemini CLI OpenAI项目将：

- ✅ 解决ByteString转换错误
- ✅ 使用最新的OAuth凭据
- ✅ 清理无用的脚本文件
- ✅ 拥有完整的备份保护

享受无缝的AI API体验！🚀
