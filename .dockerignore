# .dockerignore for Gemini CLI OpenAI Worker
# Optimizes Docker build by excluding unnecessary files

# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Git
.git
.gitignore

# Environment and config files that shouldn't be in the image
.env
.env.local
.env.*.local

# Build outputs and caches
dist
build
.wrangler
.mf
*.tsbuildinfo

# IDE and editor files
.vscode
.idea
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage
*.lcov
.nyc_output

# Documentation
README.md
CHANGELOG.md
LICENSE

# Test files (if you add them later)
test
tests
*.test.ts
*.test.js
*.spec.ts
*.spec.js

# Docker related files (to avoid recursive copying)
Dockerfile
docker-compose.yml
.dockerignore

# Examples and documentation
examples
docs

# HTTP test files
*.http
