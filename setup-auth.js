#!/usr/bin/env node

/**
 * 🔐 认证配置工具
 * 支持OAuth和API Key两种认证方式
 */

const fs = require("fs");
const path = require("path");
const readline = require("readline");

class AuthSetup {
	constructor() {
		this.devVarsPath = ".dev.vars";
		this.backupDir = "oauth_backups";
	}

	// 创建备份
	createBackup() {
		try {
			if (!fs.existsSync(this.backupDir)) {
				fs.mkdirSync(this.backupDir, { recursive: true });
			}

			if (fs.existsSync(this.devVarsPath)) {
				const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
				const backupPath = path.join(this.backupDir, `.dev.vars.backup.${timestamp}`);
				fs.copyFileSync(this.devVarsPath, backupPath);
				console.log(`💾 配置已备份: ${backupPath}`);
			}
			return true;
		} catch (error) {
			console.error("❌ 备份失败:", error.message);
			return false;
		}
	}

	// 设置API Key认证
	setupApiKey(apiKey) {
		try {
			this.createBackup();

			// 读取现有配置
			let content = "";
			if (fs.existsSync(this.devVarsPath)) {
				content = fs.readFileSync(this.devVarsPath, "utf8");
			}

			// 移除OAuth配置
			content = content.replace(/^GCP_SERVICE_ACCOUNT=.*$/m, "");

			// 添加或更新API Key
			const apiKeyLine = `GEMINI_API_KEY=${apiKey}`;
			if (content.includes("GEMINI_API_KEY=")) {
				content = content.replace(/^GEMINI_API_KEY=.*$/m, apiKeyLine);
			} else {
				content = `${apiKeyLine}\n\n${content}`;
			}

			// 确保有OpenAI API Key
			if (!content.includes("OPENAI_API_KEY=")) {
				const openaiKey = this.generateOpenAIKey();
				content = `${apiKeyLine}\n\n# OpenAI兼容API密钥\nOPENAI_API_KEY=${openaiKey}\n\n${content}`;
			}

			// 清理多余空行
			content = content.replace(/\n{3,}/g, "\n\n").trim();

			fs.writeFileSync(this.devVarsPath, content + "\n");
			console.log("✅ API Key认证配置完成");
			return true;
		} catch (error) {
			console.error("❌ API Key配置失败:", error.message);
			return false;
		}
	}

	// 设置OAuth认证
	setupOAuth(oauthCredsPath) {
		try {
			if (!fs.existsSync(oauthCredsPath)) {
				throw new Error(`OAuth凭据文件不存在: ${oauthCredsPath}`);
			}

			this.createBackup();

			const creds = JSON.parse(fs.readFileSync(oauthCredsPath, "utf8"));
			const credsJson = JSON.stringify(creds);

			// 读取现有配置
			let content = "";
			if (fs.existsSync(this.devVarsPath)) {
				content = fs.readFileSync(this.devVarsPath, "utf8");
			}

			// 移除API Key配置
			content = content.replace(/^GEMINI_API_KEY=.*$/m, "");

			// 添加或更新OAuth配置
			const gcpLine = `GCP_SERVICE_ACCOUNT=${credsJson}`;
			if (content.includes("GCP_SERVICE_ACCOUNT=")) {
				content = content.replace(/^GCP_SERVICE_ACCOUNT=.*$/m, gcpLine);
			} else {
				content = `${gcpLine}\n\n${content}`;
			}

			// 确保有OpenAI API Key
			if (!content.includes("OPENAI_API_KEY=")) {
				const openaiKey = this.generateOpenAIKey();
				content = `${gcpLine}\n\n# OpenAI兼容API密钥\nOPENAI_API_KEY=${openaiKey}\n\n${content}`;
			}

			// 清理多余空行
			content = content.replace(/\n{3,}/g, "\n\n").trim();

			fs.writeFileSync(this.devVarsPath, content + "\n");
			console.log("✅ OAuth认证配置完成");
			return true;
		} catch (error) {
			console.error("❌ OAuth配置失败:", error.message);
			return false;
		}
	}

	// 生成OpenAI兼容API密钥
	generateOpenAIKey() {
		const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
		let result = "sk-";
		for (let i = 0; i < 48; i++) {
			result += chars.charAt(Math.floor(Math.random() * chars.length));
		}
		return result;
	}

	// 显示当前配置状态
	showStatus() {
		console.log("\n📊 当前认证配置状态:");
		console.log("=".repeat(60));

		if (!fs.existsSync(this.devVarsPath)) {
			console.log("❌ 未找到配置文件");
			return;
		}

		const content = fs.readFileSync(this.devVarsPath, "utf8");
		const now = new Date();

		if (content.includes("GEMINI_API_KEY=")) {
			const match = content.match(/GEMINI_API_KEY=(.+)/);
			if (match) {
				const key = match[1].trim();
				console.log("✅ 认证方式: API Key (推荐)");
				console.log(`🔑 Gemini API Key: ${key.substring(0, 20)}...`);
				console.log("⏰ 过期时间: 🟢 永不过期");
				console.log("🔄 刷新需求: 无需刷新");
				console.log("💡 优势: 稳定可靠，无需维护");
			}
		} else if (content.includes("GCP_SERVICE_ACCOUNT=")) {
			try {
				const match = content.match(/GCP_SERVICE_ACCOUNT=(.+)/);
				if (match) {
					const creds = JSON.parse(match[1]);
					const expiryDate = new Date(creds.expiry_date);
					const timeLeft = creds.expiry_date - Date.now();
					const minutes = Math.floor(timeLeft / 60000);
					const hours = Math.floor(minutes / 60);

					console.log("✅ 认证方式: OAuth");
					console.log(`⏰ 过期时间: ${expiryDate.toLocaleString()}`);

					if (timeLeft > 0) {
						if (hours > 0) {
							console.log(`⏳ 剩余时间: 🟢 ${hours}小时${minutes % 60}分钟`);
						} else if (minutes > 10) {
							console.log(`⏳ 剩余时间: 🟡 ${minutes}分钟`);
						} else {
							console.log(`⏳ 剩余时间: 🔴 ${minutes}分钟 (即将过期)`);
						}
					} else {
						console.log("⏳ 剩余时间: 🔴 已过期");
					}

					console.log("🔄 刷新需求: 每小时自动刷新");
					console.log("💡 提示: 如遇问题可运行 node setup-auth.js --oauth");
				}
			} catch (error) {
				console.log("❌ OAuth配置格式错误");
			}
		} else {
			console.log("❌ 未配置认证方式");
			console.log("💡 请运行: node setup-auth.js");
		}

		if (content.includes("OPENAI_API_KEY=")) {
			const match = content.match(/OPENAI_API_KEY=(.+)/);
			if (match) {
				const key = match[1].trim();
				console.log(`🔗 OpenAI兼容密钥: ${key}`);
			}
		}

		console.log(`📅 检查时间: ${now.toLocaleString()}`);
		console.log("🌐 服务地址: http://127.0.0.1:8787");
		console.log("🤖 默认模型: gemini-2.5-flash");
	}
}

// 交互式配置
async function interactiveSetup() {
	const rl = readline.createInterface({
		input: process.stdin,
		output: process.stdout
	});

	const question = (prompt) => new Promise((resolve) => rl.question(prompt, resolve));

	console.log("🔐 Gemini CLI OpenAI 认证配置工具");
	console.log("=".repeat(50));

	const authSetup = new AuthSetup();
	authSetup.showStatus();

	console.log("\n📋 选择认证方式:");
	console.log("1. API Key (推荐 - 永不过期)");
	console.log("2. OAuth (每小时需要刷新)");
	console.log("3. 查看当前状态");
	console.log("4. 退出");

	const choice = await question("\n请选择 (1-4): ");

	switch (choice) {
		case "1":
			const apiKey = await question("请输入Gemini API Key: ");
			if (apiKey.trim()) {
				authSetup.setupApiKey(apiKey.trim());
				console.log("\n🎉 API Key配置完成！");
				console.log("💡 提示: 使用 npm run dev 启动服务器");
			} else {
				console.log("❌ API Key不能为空");
			}
			break;

		case "2":
			const oauthPath = await question("请输入OAuth凭据文件路径 (默认: C:\\Users\\<USER>\\.gemini\\oauth_creds.json): ");
			const finalPath = oauthPath.trim() || "C:\\Users\\<USER>\\.gemini\\oauth_creds.json";
			authSetup.setupOAuth(finalPath);
			console.log("\n🎉 OAuth配置完成！");
			console.log("💡 提示: 使用 npm run dev 启动服务器");
			break;

		case "3":
			authSetup.showStatus();
			break;

		case "4":
			console.log("👋 再见！");
			break;

		default:
			console.log("❌ 无效选择");
	}

	rl.close();
}

// 命令行参数处理
async function main() {
	const args = process.argv.slice(2);
	const authSetup = new AuthSetup();

	if (args.includes("--help") || args.includes("-h")) {
		console.log(`
🔐 认证配置工具

用法:
  node setup-auth.js                           # 交互式配置
  node setup-auth.js --api-key <key>          # 设置API Key
  node setup-auth.js --oauth <path>           # 设置OAuth
  node setup-auth.js --status                 # 查看状态

示例:
  node setup-auth.js --api-key AIzaSyDznvinSFtDL8VXi2EukEUo3UFxUexs5Y
  node setup-auth.js --oauth "C:\\Users\\<USER>\\.gemini\\oauth_creds.json"
        `);
		return;
	}

	if (args.includes("--status")) {
		authSetup.showStatus();
		return;
	}

	const apiKeyIndex = args.indexOf("--api-key");
	if (apiKeyIndex !== -1 && args[apiKeyIndex + 1]) {
		const success = authSetup.setupApiKey(args[apiKeyIndex + 1]);
		if (success) {
			console.log("\n🎉 API Key配置完成！");
			authSetup.showStatus();
		}
		return;
	}

	const oauthIndex = args.indexOf("--oauth");
	if (oauthIndex !== -1 && args[oauthIndex + 1]) {
		const success = authSetup.setupOAuth(args[oauthIndex + 1]);
		if (success) {
			console.log("\n🎉 OAuth配置完成！");
			authSetup.showStatus();
		}
		return;
	}

	// 默认交互式配置
	await interactiveSetup();
}

if (require.main === module) {
	main().catch((error) => {
		console.error("❌ 配置失败:", error.message);
		process.exit(1);
	});
}

module.exports = AuthSetup;
