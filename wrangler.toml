name = "gemini-cli-worker"
main = "src/index.ts"
compatibility_date = "2024-09-23"

compatibility_flags = ["nodejs_compat"]

# --- KV Namespaces ---
kv_namespaces = [
  { binding = "GEMINI_CLI_KV", id = "6c761101b4a34c089e6a123f2782c5fd" }
]

# [vars]


# --- Local Development Configuration ---
# This section configures local development with Docker and miniflare
[dev]
ip = "0.0.0.0"          # Bind to all interfaces for Docker access
port = 8787             # Port for local development server
# Note: --persist-to flag in <PERSON><PERSON> handles storage location

# wrangler.toml (wrangler v3.88.0^)
[observability.logs]
enabled = true
