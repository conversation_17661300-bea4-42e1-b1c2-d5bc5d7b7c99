{"name": "duckduckgo-mcp-server", "version": "1.0.0", "description": "Free DuckDuckGo MCP Server for AI web search", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "node --watch server.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.0.0", "zod": "^3.22.0", "node-fetch": "^3.3.0", "cheerio": "^1.0.0"}, "bin": {"duckduckgo-mcp": "./server.js"}, "keywords": ["mcp", "search", "duckduck<PERSON>", "ai", "free"], "author": "Your Name", "license": "MIT"}