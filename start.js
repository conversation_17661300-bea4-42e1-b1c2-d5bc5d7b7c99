#!/usr/bin/env node

/**
 * Gemini CLI OpenAI 终极启动脚本 v2.0
 * 功能：
 * - 智能清理无用文件和缓存
 * - 自动检测OAuth过期、更新凭据
 * - 生成API密钥并验证
 * - 优雅启动服务器
 * - 实时健康检查和监控
 * - 完整的错误处理和恢复
 */

const fs = require('fs');
const path = require('path');
const { spawn, exec } = require('child_process');
const os = require('os');
const http = require('http');

class GeminiStarter {
    constructor() {
        this.windowsCredsPath = path.join(os.homedir(), '.gemini', 'oauth_creds.json');
        this.devVarsPath = './.dev.vars';
        this.serverProcess = null;
        this.monitorInterval = null;
        this.healthCheckInterval = null;
        this.startTime = Date.now();

        // 解析命令行参数
        this.options = this.parseArgs();

        // 绑定清理函数
        process.on('SIGINT', () => this.gracefulShutdown());
        process.on('SIGTERM', () => this.gracefulShutdown());
        process.on('exit', () => this.cleanup());
        process.on('uncaughtException', (error) => this.handleError('Uncaught Exception', error));
        process.on('unhandledRejection', (reason, promise) => this.handleError('Unhandled Rejection', reason));
    }

    // 解析命令行参数
    parseArgs() {
        const args = process.argv.slice(2);
        return {
            help: args.includes('--help') || args.includes('-h'),
            force: args.includes('--force') || args.includes('-f'),
            skipOAuth: args.includes('--skip-oauth'),
            noMonitor: args.includes('--no-monitor'),
            verbose: args.includes('--verbose') || args.includes('-v'),
            clean: args.includes('--clean') || args.includes('-c'),
            deepClean: args.includes('--deep-clean'),
            skipHealthCheck: args.includes('--skip-health-check'),
            port: this.getArgValue(args, '--port') || '8787'
        };
    }

    // 获取参数值
    getArgValue(args, flag) {
        const index = args.indexOf(flag);
        return index !== -1 && index + 1 < args.length ? args[index + 1] : null;
    }

    // 状态输出函数
    log(message, type = 'info') {
        const colors = {
            success: '\x1b[32m', // 绿色
            warning: '\x1b[33m', // 黄色
            error: '\x1b[31m',   // 红色
            info: '\x1b[36m',    // 青色
            debug: '\x1b[90m'    // 灰色
        };

        const prefixes = {
            success: '✅',
            warning: '⚠️ ',
            error: '❌',
            info: 'ℹ️ ',
            debug: '🔍'
        };

        const reset = '\x1b[0m';
        const timestamp = new Date().toLocaleTimeString();
        
        console.log(`${colors[type]}${prefixes[type]} [${timestamp}] ${message}${reset}`);

        // 详细日志记录
        if (this.options.verbose && type === 'debug') {
            console.log(`${colors.debug}🔍 [DEBUG] ${message}${reset}`);
        }
    }

    // 错误处理
    handleError(type, error) {
        this.log(`${type}: ${error.message}`, 'error');
        if (this.options.verbose) {
            console.error(error.stack);
        }
        this.gracefulShutdown();
    }

    // 优雅关闭
    async gracefulShutdown() {
        this.log('正在优雅关闭服务器...', 'warning');

        // 停止健康检查
        if (this.healthCheckInterval) {
            clearInterval(this.healthCheckInterval);
            this.healthCheckInterval = null;
        }

        // 停止OAuth监控
        if (this.monitorInterval) {
            clearInterval(this.monitorInterval);
            this.monitorInterval = null;
        }

        // 停止服务器进程
        if (this.serverProcess) {
            this.log('正在停止服务器进程...', 'info');
            this.serverProcess.kill('SIGTERM');

            // 等待进程优雅退出
            await new Promise((resolve) => {
                const timeout = setTimeout(() => {
                    this.log('强制终止服务器进程', 'warning');
                    this.serverProcess.kill('SIGKILL');
                    resolve();
                }, 5000);

                this.serverProcess.on('exit', () => {
                    clearTimeout(timeout);
                    resolve();
                });
            });
        }

        const uptime = Math.round((Date.now() - this.startTime) / 1000);
        this.log(`服务器已停止，运行时间: ${uptime}秒`, 'success');
        process.exit(0);
    }

    // 清理临时文件和缓存
    async cleanupFiles() {
        this.log('开始清理无用文件和缓存...', 'info');

        const cleanupTargets = [
            // Wrangler缓存
            { path: '.wrangler', type: 'directory', description: 'Wrangler缓存' },
            // Node modules缓存
            { path: 'node_modules/.cache', type: 'directory', description: 'Node modules缓存' },
            // 临时文件
            { path: 'temp', type: 'directory', description: '临时文件目录' },
            { path: 'tmp', type: 'directory', description: '临时文件目录' },
            // 日志文件
            { path: '*.log', type: 'glob', description: '日志文件' },
            { path: 'logs', type: 'directory', description: '日志目录' },
            // 测试文件
            { path: 'test-*.ps1', type: 'glob', description: '测试脚本' },
            { path: 'debug-*.js', type: 'glob', description: '调试脚本' }
        ];

        if (this.options.deepClean) {
            cleanupTargets.push(
                { path: 'node_modules', type: 'directory', description: 'Node modules (深度清理)' },
                { path: 'package-lock.json', type: 'file', description: 'Package lock文件' }
            );
        }

        let cleanedCount = 0;
        for (const target of cleanupTargets) {
            try {
                if (target.type === 'glob') {
                    // 处理通配符文件
                    const files = await this.globFiles(target.path);
                    for (const file of files) {
                        if (fs.existsSync(file)) {
                            fs.unlinkSync(file);
                            this.log(`删除文件: ${file}`, 'debug');
                            cleanedCount++;
                        }
                    }
                } else if (target.type === 'directory' && fs.existsSync(target.path)) {
                    await this.removeDirectory(target.path);
                    this.log(`删除目录: ${target.path}`, 'debug');
                    cleanedCount++;
                } else if (target.type === 'file' && fs.existsSync(target.path)) {
                    fs.unlinkSync(target.path);
                    this.log(`删除文件: ${target.path}`, 'debug');
                    cleanedCount++;
                }
            } catch (error) {
                this.log(`清理 ${target.description} 失败: ${error.message}`, 'warning');
            }
        }

        this.log(`清理完成，删除了 ${cleanedCount} 个项目`, 'success');
    }

    // 简单的glob实现
    async globFiles(pattern) {
        const files = [];
        try {
            const allFiles = fs.readdirSync('.');
            const regex = new RegExp(pattern.replace(/\*/g, '.*'));
            for (const file of allFiles) {
                if (regex.test(file)) {
                    files.push(file);
                }
            }
        } catch (error) {
            // 忽略错误
        }
        return files;
    }

    // 递归删除目录
    async removeDirectory(dirPath) {
        if (!fs.existsSync(dirPath)) return;

        const files = fs.readdirSync(dirPath);
        for (const file of files) {
            const filePath = path.join(dirPath, file);
            const stat = fs.statSync(filePath);

            if (stat.isDirectory()) {
                await this.removeDirectory(filePath);
            } else {
                try {
                    fs.unlinkSync(filePath);
                } catch (error) {
                    // 忽略被锁定的文件
                }
            }
        }

        try {
            fs.rmdirSync(dirPath);
        } catch (error) {
            // 忽略非空目录错误
        }
    }

    // 检查OAuth凭据状态
    async checkOAuthCredentials() {
        this.log('检查OAuth凭据状态...', 'info');

        if (!fs.existsSync(this.windowsCredsPath)) {
            this.log(`OAuth凭据文件不存在: ${this.windowsCredsPath}`, 'error');
            return { status: 'missing', message: '凭据文件不存在' };
        }

        try {
            const credsContent = fs.readFileSync(this.windowsCredsPath, 'utf8');
            const creds = JSON.parse(credsContent);
            const now = Date.now();
            const expiryDate = creds.expiry_date;

            if (!expiryDate) {
                this.log('凭据文件格式错误，缺少过期时间', 'error');
                return { status: 'invalid', message: '凭据格式错误' };
            }

            const timeLeft = expiryDate - now;
            const minutesLeft = Math.floor(timeLeft / 60000);

            if (timeLeft <= 0) {
                this.log('OAuth Token已过期', 'error');
                return { status: 'expired', message: 'Token已过期', minutesLeft };
            } else if (minutesLeft < 5) {
                this.log(`Token将在 ${minutesLeft} 分钟后过期`, 'warning');
                return { status: 'critical', message: '即将过期', minutesLeft };
            } else if (minutesLeft < 30) {
                this.log(`Token将在 ${minutesLeft} 分钟后过期`, 'warning');
                return { status: 'warning', message: '需要关注', minutesLeft };
            } else {
                const hoursLeft = Math.floor(minutesLeft / 60);
                this.log(`Token状态正常 (剩余 ${hoursLeft} 小时 ${minutesLeft % 60} 分钟)`, 'success');
                return { status: 'valid', message: 'Token有效', minutesLeft };
            }
        } catch (error) {
            this.log(`检查OAuth凭据失败: ${error.message}`, 'error');
            return { status: 'error', message: '检查失败' };
        }
    }

    // 运行Gemini CLI进行认证
    async runGeminiAuth() {
        this.log('启动Gemini CLI进行OAuth认证...', 'info');
        this.log('请在打开的窗口中选择 "Login with Google" 并完成认证', 'warning');

        return new Promise((resolve) => {
            const geminiProcess = spawn('gemini', [], {
                stdio: 'inherit',
                shell: true
            });

            geminiProcess.on('close', (code) => {
                if (code === 0) {
                    this.log('Gemini认证完成', 'success');
                    setTimeout(() => resolve(true), 2000);
                } else {
                    this.log(`Gemini认证失败，退出代码: ${code}`, 'error');
                    resolve(false);
                }
            });

            geminiProcess.on('error', (error) => {
                this.log(`启动Gemini CLI失败: ${error.message}`, 'error');
                this.log('请确保已安装Gemini CLI: npm install -g @google/gemini-cli', 'info');
                resolve(false);
            });
        });
    }

    // 更新项目OAuth凭据
    async updateProjectCredentials() {
        this.log('更新项目OAuth凭据...', 'info');

        if (!fs.existsSync(this.windowsCredsPath)) {
            this.log('OAuth凭据文件仍然不存在', 'error');
            return false;
        }

        try {
            if (fs.existsSync('./update_oauth_credentials.js')) {
                this.log('运行OAuth凭据更新脚本...', 'info');
                
                return new Promise((resolve) => {
                    const updateProcess = spawn('node', ['update_oauth_credentials.js', this.windowsCredsPath], {
                        stdio: 'inherit'
                    });

                    updateProcess.on('close', (code) => {
                        if (code === 0) {
                            this.log('OAuth凭据更新成功', 'success');
                            resolve(true);
                        } else {
                            this.log('OAuth凭据更新失败', 'error');
                            resolve(false);
                        }
                    });

                    updateProcess.on('error', (error) => {
                        this.log(`更新OAuth凭据时出错: ${error.message}`, 'error');
                        resolve(false);
                    });
                });
            } else {
                this.log('OAuth更新脚本不存在，跳过更新', 'warning');
                return true;
            }
        } catch (error) {
            this.log(`更新OAuth凭据时出错: ${error.message}`, 'error');
            return false;
        }
    }

    // 生成API密钥
    async generateApiKey() {
        this.log('生成新的API密钥...', 'info');

        try {
            if (fs.existsSync('./setup-key.ps1')) {
                return new Promise((resolve) => {
                    const keyProcess = spawn('powershell', ['-ExecutionPolicy', 'Bypass', '-File', 'setup-key.ps1'], {
                        stdio: 'inherit'
                    });

                    keyProcess.on('close', (code) => {
                        if (code === 0) {
                            this.log('API密钥生成成功', 'success');
                            resolve(true);
                        } else {
                            this.log('API密钥生成失败', 'error');
                            resolve(false);
                        }
                    });

                    keyProcess.on('error', (error) => {
                        this.log(`生成API密钥时出错: ${error.message}`, 'error');
                        resolve(false);
                    });
                });
            } else {
                this.log('API密钥生成脚本不存在', 'warning');
                return true;
            }
        } catch (error) {
            this.log(`生成API密钥时出错: ${error.message}`, 'error');
            return false;
        }
    }

    // 启动开发服务器
    async startDevServer() {
        this.log('启动Gemini CLI OpenAI开发服务器...', 'info');

        try {
            // 检查端口是否被占用
            await this.killExistingProcesses();

            this.log(`执行: npm run dev (端口: ${this.options.port})`, 'debug');

            // 设置环境变量
            const env = { ...process.env };
            if (this.options.port !== '8787') {
                env.PORT = this.options.port;
            }

            this.serverProcess = spawn('npm', ['run', 'dev'], {
                stdio: 'inherit',
                shell: true,
                env: env
            });

            // 监听服务器进程事件
            this.serverProcess.on('error', (error) => {
                this.log(`服务器进程错误: ${error.message}`, 'error');
            });

            // 等待服务器启动
            this.log('等待服务器启动...', 'info');
            await this.waitForServerReady();

            // 验证服务器健康状态
            if (!this.options.skipHealthCheck) {
                const healthCheck = await this.performHealthCheck();
                if (!healthCheck.success) {
                    this.log('服务器健康检查失败，但继续运行...', 'warning');
                } else {
                    this.log('服务器健康检查通过！', 'success');
                }
            }

            // 验证API密钥
            const apiKeyCheck = await this.validateApiKey();
            if (apiKeyCheck.success) {
                this.log('API密钥验证通过！', 'success');
            } else {
                this.log('API密钥验证失败，请检查配置', 'warning');
            }

            this.log(`服务器启动成功！`, 'success');
            this.log(`本地地址: http://127.0.0.1:${this.options.port}`, 'info');
            this.log(`网络地址: http://*************:${this.options.port}`, 'info');

            return true;
        } catch (error) {
            this.log(`启动服务器失败: ${error.message}`, 'error');
            return false;
        }
    }

    // 等待服务器准备就绪
    async waitForServerReady() {
        this.log('等待服务器准备就绪...', 'info');

        // 首先等待固定时间让服务器启动
        await new Promise(resolve => setTimeout(resolve, 8000));

        const maxAttempts = 15; // 15次尝试
        const interval = 2000; // 2秒间隔

        for (let attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                // 尝试多个端点，任何一个成功就认为服务器准备就绪
                const endpoints = ['/health', '/', '/v1/models'];
                let success = false;

                for (const endpoint of endpoints) {
                    try {
                        await this.makeHttpRequest(`http://127.0.0.1:${this.options.port}${endpoint}`, 'GET', null, 3000);
                        success = true;
                        break;
                    } catch (error) {
                        // 继续尝试下一个端点
                    }
                }

                if (success) {
                    this.log(`服务器在第${attempt}次尝试后准备就绪`, 'debug');
                    return true;
                }
            } catch (error) {
                // 忽略错误，继续尝试
            }

            if (attempt === maxAttempts) {
                this.log('服务器健康检查超时，但继续启动...', 'warning');
                return true; // 不要因为健康检查失败而停止启动
            }

            this.log(`等待服务器响应... (${attempt}/${maxAttempts})`, 'debug');
            await new Promise(resolve => setTimeout(resolve, interval));
        }
    }

    // 执行健康检查
    async performHealthCheck() {
        this.log('执行服务器健康检查...', 'info');

        const checks = [
            { name: '健康端点', url: `/health`, expected: 'ok' },
            { name: '模型列表', url: `/v1/models`, expected: 'data' },
            { name: 'API密钥检查', url: `/v1/debug/api-key-check`, expected: 'status' }
        ];

        const results = [];
        for (const check of checks) {
            try {
                const response = await this.makeHttpRequest(
                    `http://127.0.0.1:${this.options.port}${check.url}`,
                    'GET',
                    null,
                    5000
                );

                const success = typeof response === 'object' &&
                               (response[check.expected] !== undefined ||
                                (Array.isArray(response.data) && response.data.length > 0));

                results.push({ name: check.name, success, response });
                this.log(`${check.name}: ${success ? '✅ 通过' : '❌ 失败'}`, success ? 'debug' : 'warning');
            } catch (error) {
                results.push({ name: check.name, success: false, error: error.message });
                this.log(`${check.name}: ❌ 失败 - ${error.message}`, 'warning');
            }
        }

        const successCount = results.filter(r => r.success).length;
        const success = successCount >= 2; // 至少2个检查通过

        return { success, results, successCount, totalCount: checks.length };
    }

    // 验证API密钥
    async validateApiKey() {
        try {
            const currentApiKey = this.getCurrentApiKey();
            if (!currentApiKey || currentApiKey === 'sk-your-secret-api-key-here') {
                return { success: false, reason: 'API密钥未设置或使用默认值' };
            }

            const response = await this.makeHttpRequest(
                `http://127.0.0.1:${this.options.port}/v1/debug/api-key-check`,
                'GET',
                null,
                5000,
                { 'Authorization': `Bearer ${currentApiKey}` }
            );

            if (response && response.keys_match === true) {
                return {
                    success: true,
                    apiKey: currentApiKey,
                    serverKeyLength: response.server_key_length,
                    clientKeyLength: response.client_key_length
                };
            } else {
                return {
                    success: false,
                    reason: 'API密钥不匹配',
                    details: response
                };
            }
        } catch (error) {
            return { success: false, reason: error.message };
        }
    }

    // HTTP请求工具
    async makeHttpRequest(url, method = 'GET', data = null, timeout = 5000, headers = {}) {
        return new Promise((resolve, reject) => {
            const urlObj = new URL(url);
            const options = {
                hostname: urlObj.hostname,
                port: urlObj.port,
                path: urlObj.pathname + urlObj.search,
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    ...headers
                },
                timeout: timeout
            };

            if (data && method !== 'GET') {
                const postData = JSON.stringify(data);
                options.headers['Content-Length'] = Buffer.byteLength(postData);
            }

            const req = http.request(options, (res) => {
                let responseData = '';

                res.on('data', (chunk) => {
                    responseData += chunk;
                });

                res.on('end', () => {
                    try {
                        const parsed = JSON.parse(responseData);
                        resolve(parsed);
                    } catch (error) {
                        if (responseData.trim() === 'ok') {
                            resolve({ status: 'ok' });
                        } else {
                            resolve(responseData);
                        }
                    }
                });
            });

            req.on('error', (error) => {
                reject(error);
            });

            req.on('timeout', () => {
                req.destroy();
                reject(new Error('Request timeout'));
            });

            if (data && method !== 'GET') {
                req.write(JSON.stringify(data));
            }

            req.end();
        });
    }

    // 终止现有进程
    async killExistingProcesses() {
        try {
            if (process.platform === 'win32') {
                exec('taskkill /F /IM wrangler.exe 2>nul', () => {});
                exec('netstat -ano | findstr :8787', (error, stdout) => {
                    if (stdout) {
                        this.log('端口8787被占用，尝试终止现有进程...', 'warning');
                        const lines = stdout.split('\n');
                        lines.forEach(line => {
                            const match = line.match(/\s+(\d+)$/);
                            if (match) {
                                exec(`taskkill /F /PID ${match[1]} 2>nul`, () => {});
                            }
                        });
                    }
                });
            }
            await new Promise(resolve => setTimeout(resolve, 2000));
        } catch (error) {
            // 忽略错误
        }
    }

    // 启动OAuth状态监控
    startOAuthMonitor() {
        if (this.options.noMonitor) {
            return;
        }

        this.log('启动OAuth状态监控...', 'info');

        this.monitorInterval = setInterval(async () => {
            if (fs.existsSync(this.windowsCredsPath)) {
                try {
                    const credsContent = fs.readFileSync(this.windowsCredsPath, 'utf8');
                    const creds = JSON.parse(credsContent);
                    const now = Date.now();
                    const timeLeft = creds.expiry_date - now;
                    const minutesLeft = Math.floor(timeLeft / 60000);

                    if (minutesLeft < 10) {
                        this.log(`警告: OAuth Token将在 ${minutesLeft} 分钟后过期！`, 'warning');
                        this.log('建议运行: node start.js --force 更新凭据', 'warning');
                    }
                } catch (error) {
                    // 忽略监控错误
                }
            }
        }, 300000); // 每5分钟检查一次

        this.log('OAuth监控已启动 (后台运行)', 'success');
    }

    // 启动健康监控
    startHealthMonitor() {
        this.log('启动服务器健康监控...', 'info');

        let consecutiveFailures = 0;
        const maxFailures = 3;

        this.healthCheckInterval = setInterval(async () => {
            try {
                const health = await this.performHealthCheck();

                if (health.success) {
                    consecutiveFailures = 0;
                    this.log(`健康检查通过 (${health.successCount}/${health.totalCount})`, 'debug');
                } else {
                    consecutiveFailures++;
                    this.log(`健康检查失败 (${consecutiveFailures}/${maxFailures}) - ${health.successCount}/${health.totalCount} 通过`, 'warning');

                    if (consecutiveFailures >= maxFailures) {
                        this.log('服务器健康检查连续失败，可能需要重启', 'error');
                        // 可以在这里添加自动重启逻辑
                    }
                }
            } catch (error) {
                consecutiveFailures++;
                this.log(`健康检查异常: ${error.message}`, 'warning');
            }
        }, 60000); // 每分钟检查一次

        this.log('健康监控已启动 (后台运行)', 'success');
    }

    // 获取当前API密钥
    getCurrentApiKey() {
        try {
            if (fs.existsSync(this.devVarsPath)) {
                const content = fs.readFileSync(this.devVarsPath, 'utf8');
                const lines = content.split('\n');
                const apiKeyLine = lines.find(line => line.startsWith('OPENAI_API_KEY='));

                if (apiKeyLine) {
                    return apiKeyLine.split('=')[1].trim();
                }
            }
            return 'sk-your-secret-api-key-here';
        } catch (error) {
            return 'sk-your-secret-api-key-here';
        }
    }

    // 显示帮助信息
    showHelp() {
        console.log(`
🚀 Gemini CLI OpenAI 终极启动脚本 v2.0

用法:
    node start.js [选项]

选项:
    --force, -f           强制重新认证，即使Token未过期
    --skip-oauth          跳过OAuth检查，直接启动服务器
    --no-monitor          不启动OAuth状态监控和健康检查
    --clean, -c           启动前清理无用文件和缓存
    --deep-clean          深度清理（包括node_modules）
    --skip-health-check   跳过服务器健康检查
    --port <端口>         指定服务器端口（默认8787）
    --verbose, -v         显示详细日志
    --help, -h            显示帮助信息

功能:
    ✅ 智能清理无用文件和缓存
    ✅ 自动检测OAuth Token过期状态
    ✅ 过期时自动启动Gemini CLI认证
    ✅ 自动更新项目OAuth凭据
    ✅ 自动生成和验证API密钥
    ✅ 优雅启动开发服务器
    ✅ 实时健康检查和监控
    ✅ 完整的错误处理和恢复
    ✅ 优雅关闭和清理

示例:
    node start.js                    # 标准启动流程
    node start.js --force            # 强制重新认证
    node start.js --clean            # 清理后启动
    node start.js --deep-clean       # 深度清理后启动
    node start.js --skip-oauth       # 跳过OAuth检查
    node start.js --no-monitor       # 不启动监控
    node start.js --port 8788        # 使用自定义端口
    node start.js --verbose --clean  # 详细日志 + 清理
`);
    }

    // 清理函数（同步版本，用于进程退出）
    cleanup() {
        // 停止健康检查
        if (this.healthCheckInterval) {
            clearInterval(this.healthCheckInterval);
            this.healthCheckInterval = null;
        }

        // 停止OAuth监控
        if (this.monitorInterval) {
            clearInterval(this.monitorInterval);
            this.monitorInterval = null;
        }

        // 停止服务器进程
        if (this.serverProcess && !this.serverProcess.killed) {
            this.serverProcess.kill('SIGTERM');
        }
    }

    // 主程序
    async run() {
        console.log(`
🚀 Gemini CLI OpenAI 终极启动脚本 v2.0
=====================================
✨ 智能清理 | 🔐 OAuth管理 | 🔑 API密钥验证 | 🏥 健康监控
`);

        // 检查是否在正确目录
        if (!fs.existsSync(this.devVarsPath)) {
            this.log('错误: 请在gemini-cli-openai项目目录中运行此脚本', 'error');
            this.log(`当前目录: ${process.cwd()}`, 'debug');
            process.exit(1);
        }

        if (this.options.help) {
            this.showHelp();
            process.exit(0);
        }

        try {
            // 0. 清理无用文件（如果需要）
            if (this.options.clean || this.options.deepClean) {
                await this.cleanupFiles();
            }

            // 1. 检查OAuth状态
            if (!this.options.skipOAuth) {
                const oauthStatus = await this.checkOAuthCredentials();

                if (this.options.force || ['missing', 'expired', 'critical'].includes(oauthStatus.status)) {
                    this.log('需要更新OAuth凭据', 'warning');

                    // 运行Gemini认证
                    if (!(await this.runGeminiAuth())) {
                        this.log('OAuth认证失败，无法继续', 'error');
                        process.exit(1);
                    }

                    // 更新项目凭据
                    if (!(await this.updateProjectCredentials())) {
                        this.log('更新项目凭据失败', 'error');
                        process.exit(1);
                    }
                } else {
                    this.log('OAuth凭据状态良好，继续启动...', 'success');
                }
            } else {
                this.log('跳过OAuth检查', 'warning');
            }

            // 2. 生成API密钥
            if (!(await this.generateApiKey())) {
                this.log('API密钥生成失败，但继续启动...', 'warning');
            }

            // 3. 启动服务器
            if (!(await this.startDevServer())) {
                this.log('服务器启动失败', 'error');
                process.exit(1);
            }

            // 4. 启动监控
            this.startOAuthMonitor();

            // 5. 启动健康监控
            if (!this.options.noMonitor) {
                this.startHealthMonitor();
            }

            // 5. 显示完成信息
            this.log('🎉 启动完成！', 'success');

            // 获取当前API密钥
            const currentApiKey = this.getCurrentApiKey();

            const uptime = Math.round((Date.now() - this.startTime) / 1000);
            const features = [];
            if (!this.options.skipOAuth) features.push('🔐 OAuth');
            if (!this.options.noMonitor) features.push('🏥 健康监控');
            if (this.options.clean || this.options.deepClean) features.push('✨ 已清理');

            console.log(`
📋 服务信息:
   🌐 本地地址: http://127.0.0.1:${this.options.port}
   🌐 网络地址: http://*************:${this.options.port}
   🤖 推荐模型: gemini-2.5-flash
   ⏱️  启动耗时: ${uptime}秒
   🔧 启用功能: ${features.join(' | ')}

📱 Roo/Cline配置:
   • Base URL: http://127.0.0.1:${this.options.port}/v1
   • API Key: ${currentApiKey}
   • Model: gemini-2.5-flash

🔍 调试端点:
   • 健康检查: http://127.0.0.1:${this.options.port}/health
   • API密钥验证: http://127.0.0.1:${this.options.port}/v1/debug/api-key-check
   • 模型列表: http://127.0.0.1:${this.options.port}/v1/models

⌨️  按 Ctrl+C 优雅停止服务器`);

            // 保持脚本运行
            this.log('服务器正在运行，按 Ctrl+C 停止...', 'info');
            
            // 监控服务器进程
            if (this.serverProcess) {
                this.serverProcess.on('exit', (code) => {
                    this.log(`服务器进程意外退出，代码: ${code}`, 'error');
                    process.exit(1);
                });
            }

            // 保持进程运行
            await new Promise(() => {}); // 永远等待，直到被中断

        } catch (error) {
            this.log(`启动过程中发生错误: ${error.message}`, 'error');
            process.exit(1);
        }
    }
}

// 执行主程序
if (require.main === module) {
    const starter = new GeminiStarter();
    starter.run().catch(error => {
        console.error('启动失败:', error.message);
        process.exit(1);
    });
}

module.exports = GeminiStarter;
